<script setup lang="ts">
import type { DropdownMenuItem } from '@nuxt/ui';

const { signOut, data } = useAuth();
const { theme, setTheme } = useTheme();

const userName = computed(() => data.value?.user?.name);
const userImage = computed(() => data.value?.user?.image);
const userEmail = computed(() => data.value?.user?.email);

const themeOptions = [
    { value: 'system', label: 'System', icon: 'i-lucide-monitor' },
    { value: 'light', label: 'Light', icon: 'i-lucide-sun' },
    { value: 'dark', label: 'Dark', icon: 'i-lucide-moon' },
] as const;

const items = computed<DropdownMenuItem[][]>(() => [
    [
        {
            label: userName.value || 'Unknown User',
            type: 'label',
        },
        ...(userEmail.value
            ? [{
                    label: userEmail.value,
                    type: 'label' as const,
                    class: 'text-gray-500 dark:text-gray-400 text-xs -mt-3',
                }]
            : []),
    ],
    [
        {
            label: 'Theme',
            icon: 'i-lucide-palette',
            children: themeOptions.map(option => ({
                label: option.label,
                icon: option.icon,
                class: 'cursor-pointer',
                onSelect: () => setTheme(option.value),
                ...(theme.value === option.value && {
                    class: 'cursor-pointer bg-gray-100 dark:bg-gray-800',
                }),
            })),
        },
        {
            class: 'cursor-pointer',
            label: 'Settings',
            icon: 'i-lucide-cog',
            to: '/settings',
        },
    ],
    [
        {
            class: 'cursor-pointer',
            label: 'Logout',
            icon: 'i-lucide-log-out',
            onSelect() {
                signOut();
            },
        },
    ],
]);
</script>

<template>
    <UDropdownMenu
        :items="items"
        size="md"
    >
        <UAvatar
            :src="userImage"
            :alt="userName"
            size="md"
            class="cursor-pointer"
        />
    </UDropdownMenu>
</template>
