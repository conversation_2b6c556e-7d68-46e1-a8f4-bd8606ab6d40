<script setup lang="ts">
const { data } = useAuth();

const userName = computed(() => data.value?.user?.name);
</script>

<template>
    <div class="p-6">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                Settings
            </h1>

            <div class="space-y-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b bg-gray-100/50 dark:bg-gray-900/50 border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                            <UIcon
                                name="i-lucide-user"
                                class="h-5 w-5 mr-2"
                            />
                            Profile
                        </h2>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Manage your profile information and preferences.
                        </p>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Display Name
                                </label>
                                <input
                                    type="text"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    :value="userName"
                                >
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                    <div class="px-6 bg-gray-100/50 dark:bg-gray-900/50 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                            <UIcon
                                name="i-lucide-bell"
                                class="h-5 w-5 mr-2"
                            />
                            Notifications
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                                        Email Notifications
                                    </h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        Receive notifications via email
                                    </p>
                                </div>
                                <USwitch
                                    color="primary"
                                    size="lg"
                                />
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                                        Push Notifications
                                    </h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        Receive push notifications in browser
                                    </p>
                                </div>
                                <USwitch
                                    color="primary"
                                    size="lg"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
