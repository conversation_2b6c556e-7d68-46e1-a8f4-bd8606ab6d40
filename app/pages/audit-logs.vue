<script setup lang="ts">
import { h, resolveComponent } from 'vue';
import { getSortedRowModel, getFilteredRowModel } from '@tanstack/vue-table';
import type { TableColumn } from '@nuxt/ui';

// @ts-expect-error suppresses the error "cannot find name '__VLS_placeholder'"
const table = useTemplateRef('table');

const UButton = resolveComponent('UButton');

type Payment = {
    date: string;
    actor: string;
    action: string;
};

// Generate more data for infinite scroll demonstration
const generateMoreData = (count: number): Payment[] => {
    const actors = [
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>',
    ];

    const actions = [
        'created a new user', 'updated user information', 'deleted a user',
        'created a new role', 'updated role permissions', 'deleted a role',
        'assigned a role to a user', 'revoked a role from a user',
    ];

    return Array.from({ length: count }, () => ({
        date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        actor: actors[Math.floor(Math.random() * actors.length)] || '<EMAIL>',
        action: actions[Math.floor(Math.random() * actions.length)] || 'Unknown action',
    }));
};

const allData = ref<Payment[]>([
    ...generateMoreData(20),
    ...generateMoreData(50), // Generate more initial data
]);

const displayedData = ref<Payment[]>(allData.value.slice(0, 50));
const isLoading = ref(false);
const hasMore = ref(true);

// Global filter state
const globalFilter = ref('');

// Expanded rows state
const expanded = ref<Record<string, boolean>>({});

// Sorting state
const sorting = ref([{ id: 'date', desc: true }]);

// Infinite scroll logic
const loadMore = async () => {
    if (isLoading.value || !hasMore.value) return;

    isLoading.value = true;

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const currentLength = displayedData.value.length;
    const nextBatch = allData.value.slice(currentLength, currentLength + 20);

    if (nextBatch.length === 0) {
        // Generate more data if we've reached the end
        const newData = generateMoreData(20);
        allData.value.push(...newData);
        displayedData.value.push(...newData);
    }
    else {
        displayedData.value.push(...nextBatch);
    }

    if (displayedData.value.length >= 200) {
        hasMore.value = false;
    }

    isLoading.value = false;
};

// Scroll event handler
const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement;
    const { scrollTop, scrollHeight, clientHeight } = target;

    if (scrollHeight - scrollTop <= clientHeight + 100) {
        loadMore();
    }
};
const columns: TableColumn<Payment>[] = [
    {
        id: 'expand',
        cell: ({ row }) =>
            h(UButton, {
                'class': 'cursor-pointer',
                'color': 'neutral',
                'variant': 'ghost',
                'icon': 'i-lucide-chevron-down',
                'square': true,
                'aria-label': 'Expand',
                'ui': {
                    leadingIcon: [
                        'transition-transform',
                        row.getIsExpanded() ? 'duration-200 rotate-180' : '',
                    ],
                },
                'onClick': () => row.toggleExpanded(),
            }),
        enableSorting: false,
    },
    {
        accessorKey: 'date',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Date',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: 'cursor-pointer -mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => {
            return new Date(row.getValue('date')).toLocaleString('en-US', {
                day: 'numeric',
                month: 'short',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true,
            });
        },
        enableSorting: true,
    },
    {
        accessorKey: 'actor',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Actor',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: 'cursor-pointer -mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        enableSorting: true,
    },
    {
        accessorKey: 'action',
        header: 'Action',
        enableSorting: true,
    },
];
</script>

<template>
    <div class="flex flex-col h-full overflow-hidden">
        <div class="p-6 flex-shrink-0">
            <div class="max-w-7xl mx-auto">
                <div class="flex items-center justify-between">
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                        Audit Logs
                    </h1>

                    <!-- Global Filter Input -->
                    <UInput
                        v-model="globalFilter"
                        placeholder="Search all columns..."
                        icon="i-lucide-search"
                        size="md"
                    />
                </div>
            </div>
        </div>

        <div class="flex-1 px-6 overflow-hidden">
            <div class="max-w-7xl mx-auto h-[calc(100%-24px)]">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 h-full flex flex-col">
                    <div
                        class="flex-1 overflow-auto"
                        @scroll="handleScroll"
                    >
                        <UTable
                            ref="table"
                            v-model:expanded="expanded"
                            v-model:sorting="sorting"
                            v-model:global-filter="globalFilter"
                            :data="displayedData"
                            :columns="columns"
                            :sorting-options="{
                                getSortedRowModel: getSortedRowModel,
                            }"
                            :global-filter-options="{
                                getFilteredRowModel: getFilteredRowModel,
                            }"
                            :loading="isLoading"
                            sticky="header"
                            class="h-full"
                            :ui="{
                                tr: 'data-[expanded=true]:bg-elevated/50',
                                thead: 'bg-gray-100/50 dark:bg-gray-900/50',
                                root: 'h-full',
                            }"
                        >
                            <template #expanded="{ row }">
                                <div class="p-4 bg-gray-50 dark:bg-gray-900/50">
                                    <h4 class="font-semibold mb-2">
                                        Payment Details
                                    </h4>
                                    <div class="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span class="font-medium">Date:</span>
                                            <span class="ml-2">{{ new Date(row.original.date).toLocaleString() }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium">Actor:</span>
                                            <span class="ml-2">{{ row.original.actor }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium">Action:</span>
                                            <span class="ml-2">{{ row.original.action }}</span>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <pre class="text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded">{{ JSON.stringify(row.original, null, 2) }}</pre>
                                    </div>
                                </div>
                            </template>
                        </UTable>
                    </div>

                    <!-- Loading indicator for infinite scroll -->
                    <div
                        v-if="isLoading"
                        class="p-4 text-center border-t border-gray-200 dark:border-gray-700"
                    >
                        <div class="flex items-center justify-center space-x-2">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary" />
                            <span class="text-sm text-gray-600 dark:text-gray-400">Loading more...</span>
                        </div>
                    </div>

                    <!-- End of data indicator -->
                    <div
                        v-else-if="!hasMore"
                        class="p-4 text-center border-t border-gray-200 dark:border-gray-700"
                    >
                        <span class="text-sm text-gray-600 dark:text-gray-400">No more data to load</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
